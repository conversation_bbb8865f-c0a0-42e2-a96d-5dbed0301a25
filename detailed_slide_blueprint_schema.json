{"$defs": {"ChartBlueprintSchema": {"description": "图表蓝图的详细定义，包含完整的Chart.js配置", "properties": {"type": {"const": "chart", "default": "chart", "description": "图表的具体类型。", "title": "Type", "type": "string"}, "title": {"description": "图表的清晰、描述性标题，例如'销售趋势 (2022-2025E)'、'市场份额分布'。", "maxLength": 50, "title": "Title", "type": "string"}, "chart_type": {"description": "图表的类型，必须是这几种之一。选择应基于数据特性：bar=对比数据，line=趋势数据，pie=占比数据，radar=多维评估，doughnut=占比数据（中心可放文字）。", "enum": ["bar", "line", "pie", "radar", "doughnut"], "title": "Chart Type", "type": "string"}, "data_fabrication_instruction": {"description": "用于生成图表数据的详细文字描述。必须包含足够的信息来创造出逼真的、符合逻辑的数据。例如：'创建一个双Y轴折线图，展示2022到2025年的销售额和销售面积趋势。销售额数据为13.3, 11.7, 9.7, 9.4万亿元；销售面积数据为13.6, 11.2, 9.7, 15.0亿平米。使用蓝色和橙色作为线条颜色。'", "minLength": 50, "title": "Data Fabrication Instruction", "type": "string"}, "final_chart_js_config": {"$ref": "#/$defs/ChartConfig", "description": "[此字段由AI填充] 最终生成的、完整的、可直接被Chart.js使用的配置对象，必须包含type, data, options三个键。data必须包含labels和datasets数组。"}, "target_area": {"description": "此图表在布局模板中的目标区域ID，例如 'chart_area_1', 'main_chart_area'。", "title": "Target Area", "type": "string"}, "animation_style": {"default": "slide-in-up", "description": "入场动画建议，例如 'zoom-in', 'slide-in-up'。图表建议使用缩放或滑入效果。", "title": "Animation Style", "type": "string"}}, "required": ["title", "chart_type", "data_fabrication_instruction", "final_chart_js_config", "target_area"], "title": "ChartBlueprintSchema", "type": "object"}, "ChartConfig": {"properties": {"chart_canvas_id": {"title": "Chart Canvas Id", "type": "string"}, "chart_type": {"enum": ["bar", "line", "pie", "radar", "scatter", "doughnut"], "title": "Chart Type", "type": "string"}, "chart_js_data": {"title": "Chart Js Data", "type": "object"}, "chart_js_options": {"anyOf": [{"type": "object"}, {"type": "null"}], "default": null, "title": "Chart Js Options"}, "chart_title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Chart Title"}, "data_source_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Data Source Description"}}, "required": ["chart_canvas_id", "chart_type", "chart_js_data"], "title": "ChartConfig", "type": "object"}, "ImageElementSchema": {"description": "图片元素的详细定义", "properties": {"type": {"const": "image", "default": "image", "description": "图片的具体类型。", "title": "Type", "type": "string"}, "generation_prompt": {"description": "用于AI绘画或图片搜索的高质量、详细的英文描述。需要包含场景、主体、风格、氛围等信息。例如：'A modern office building with glass facade at sunset, professional business atmosphere, architectural photography style, warm lighting, urban skyline in background'。", "minLength": 20, "title": "Generation Prompt", "type": "string"}, "alt_text": {"description": "图片的alt属性文本，用于无障碍访问。应简洁描述图片内容。", "maxLength": 100, "title": "Alt Text", "type": "string"}, "target_area": {"description": "此图片在布局模板中的目标区域ID，例如 'image_area', 'hero_image_area'。", "title": "Target Area", "type": "string"}, "animation_style": {"default": "fade-in", "description": "入场动画建议，例如 'fade-in', 'slide-in-right'。图片建议使用渐显效果。", "title": "Animation Style", "type": "string"}}, "required": ["generation_prompt", "alt_text", "target_area"], "title": "ImageElementSchema", "type": "object"}, "KpiCardSchema": {"description": "KPI数据卡片的详细定义", "properties": {"type": {"const": "kpi_card", "default": "kpi_card", "description": "KPI卡片的具体类型。", "title": "Type", "type": "string"}, "title": {"description": "KPI卡片的标题，例如'新房销售额'、'市场增长率'。应简洁明了，不超过10个字符。", "maxLength": 20, "title": "Title", "type": "string"}, "value": {"description": "KPI的核心数值，必须是一个引人注目的、格式化的字符串，例如'9.4万亿元'、'+15.2%'、'1,234万套'。必须包含单位和具体数值，避免使用占位符。数值应该逼真且符合行业常识。", "minLength": 1, "title": "Value", "type": "string"}, "change": {"default": "持平", "description": "与前期对比的变化值，例如'-3.2%'、'+15.8%'、'↑12.5%'、'↓8.1%'。必须包含方向指示符（+/-/↑/↓）和具体的百分比数值，不能使用模糊的描述。", "title": "Change", "type": "string"}, "icon_fontawesome_class": {"description": "用于表示此KPI的Font Awesome 6的图标完整类名，例如'fa-solid fa-chart-line'、'fa-solid fa-money-bill-trend-up'。必须是有效的FA6类名。", "title": "Icon Fontawesome Class", "type": "string"}, "target_area": {"description": "此元素在布局模板中的目标区域ID，例如 'kpi_card_1', 'kpi_card_2'。", "title": "Target Area", "type": "string"}, "animation_style": {"default": "fade-in", "description": "入场动画建议，例如 'fade-in', 'slide-in-left'。KPI卡片建议使用渐显效果。", "title": "Animation Style", "type": "string"}}, "required": ["title", "value", "icon_fontawesome_class", "target_area"], "title": "KpiCardSchema", "type": "object"}, "TextElementSchema": {"properties": {"type": {"description": "文本元素的具体类型。可选值：'title', 'subtitle', 'paragraph', 'bullet_point', 'kicker'。title=主标题，subtitle=副标题，paragraph=段落文本，bullet_point=要点列表项，kicker=引导性文字", "enum": ["title", "subtitle", "paragraph", "bullet_point", "kicker"], "title": "Type", "type": "string"}, "content": {"description": "该文本元素的具体内容。必须是简洁、有力、符合商业演示标准的文案。避免使用占位符文本。", "maxLength": 500, "minLength": 1, "title": "Content", "type": "string"}, "target_area": {"description": "此元素在布局模板中应放置的目标区域ID，例如 'title_area', 'main_content_area', 'subtitle_area'。必须与选定的layout_template_name兼容。", "title": "Target Area", "type": "string"}, "animation_style": {"default": "fade-in", "description": "建议的入场动画CSS类名，例如 'slide-in-up', 'fade-in', 'zoom-in'。选择应与内容重要性匹配。", "title": "Animation Style", "type": "string"}}, "required": ["type", "content", "target_area"], "title": "TextElementSchema", "type": "object"}}, "description": "A comprehensive blueprint for generating a single presentation slide with enhanced validation and constraints.", "properties": {"slide_number": {"description": "幻灯片的顺序号，从1开始。", "maximum": 50, "minimum": 1, "title": "Slide Number", "type": "integer"}, "layout_template_name": {"description": "布局模板的名称，必须从预定义的模板中选择。可选值：'TitleSlideLayout'(封面页), 'DataDashboardLayout'(数据仪表板), 'ContentSlideLayout'(内容页), 'PolicyAnalysisLayout'(政策分析), 'ComparisonLayout'(对比分析), 'TimelineLayout'(时间线), 'ProcessFlowLayout'(流程图)。选择应基于幻灯片内容类型。", "title": "Layout Template Name", "type": "string"}, "background_style_description": {"description": "对背景的详细CSS描述，例如 'linear-gradient(135deg, #0A1931 0%, #1E293B 100%)' 或 'radial-gradient(circle at center, #f8fafc 0%, #e2e8f0 100%)'。应与整体风格保持一致。", "minLength": 10, "title": "Background Style Description", "type": "string"}, "key_elements": {"description": "构成此幻灯片核心内容的所有元素的列表。每个元素都必须有'type'字段以进行区分。支持的类型：TextElement(title, subtitle, paragraph, bullet_point, kicker)、KpiCardSchema(kpi_card)、ChartBlueprintSchema(chart)、ImageElementSchema(image)。列表不能为空，至少包含一个元素。", "items": {"discriminator": {"mapping": {"bullet_point": "#/$defs/TextElementSchema", "chart": "#/$defs/ChartBlueprintSchema", "image": "#/$defs/ImageElementSchema", "kicker": "#/$defs/TextElementSchema", "kpi_card": "#/$defs/KpiCardSchema", "paragraph": "#/$defs/TextElementSchema", "subtitle": "#/$defs/TextElementSchema", "title": "#/$defs/TextElementSchema"}, "propertyName": "type"}, "oneOf": [{"$ref": "#/$defs/TextElementSchema"}, {"$ref": "#/$defs/KpiCardSchema"}, {"$ref": "#/$defs/ChartBlueprintSchema"}, {"$ref": "#/$defs/ImageElementSchema"}]}, "minItems": 1, "title": "Key Elements", "type": "array"}, "speaker_notes": {"description": "演讲者的备注，用于对幻灯片内容进行补充说明或提供演讲提示。内容需要有深度和见解，不少于50字符。必须包含：1)关键数据的深度解读和背景分析；2)数据趋势的商业意义；3)具体的演讲建议和重点强调内容。避免简单复述幻灯片内容。", "maxLength": 1000, "minLength": 50, "title": "Speaker Notes", "type": "string"}}, "required": ["slide_number", "layout_template_name", "background_style_description", "key_elements", "speaker_notes"], "title": "Detailed Slide Blueprint", "type": "object"}