2025-06-18 22:38:47,451 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT 
type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x20a628586b0 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:38:47,461 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_a67b1dd9-630a-4619-b672-6c506a644a8f_Slide_6] 详细蓝图生成失败，
LLM返回空响应或Pydantic验证失败。
2025-06-18 22:38:47,461 [ERROR] [project_generation.a67b1dd9-630a-4619-b672-6c506a644a8f:417] - 第 6 张幻灯片蓝图细化失败
2025-06-18 22:38:47,596 [INFO] [project_generation.a67b1dd9-630a-4619-b672-6c506a644a8f:212] - 开始细化第 7 张幻灯片蓝图...
2025-06-18 22:38:47,596 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_a67b1dd9-630a-4619-b672-6c506a644a8f_Slide_7] 正在根据大纲细化第 
7 张幻灯片的详细蓝图...
2025-06-18 22:38:47,602 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...5iK0'. Usage in last 60s: 2/5.
2025-06-18 22:38:47,723 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...5iK0'
2025-06-18 22:38:47,726 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:38:48,450 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:38:48,451 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT 
type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x20a62865750 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e
instructor.exceptions.InstructorRetryException: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}
2025-06-18 22:38:48,460 [ERROR] [app.agents.slide_detailer_agent:186] - [DetailBlueprint_Proj_a67b1dd9-630a-4619-b672-6c506a644a8f_Slide_7] 详细蓝图生成失败，
LLM返回空响应或Pydantic验证失败。
2025-06-18 22:38:48,460 [ERROR] [project_generation.a67b1dd9-630a-4619-b672-6c506a644a8f:417] - 第 7 张幻灯片蓝图细化失败
2025-06-18 22:38:48,603 [INFO] [project_generation.a67b1dd9-630a-4619-b672-6c506a644a8f:212] - 开始细化第 8 张幻灯片蓝图...
2025-06-18 22:38:48,603 [INFO] [app.agents.slide_detailer_agent:155] - [DetailBlueprint_Proj_a67b1dd9-630a-4619-b672-6c506a644a8f_Slide_8] 正在根据大纲细化第 
8 张幻灯片的详细蓝图...
2025-06-18 22:38:48,609 [INFO] [app.utils.api_key_manager:85] - Issuing API Key ending in '...ChBw'. Usage in last 60s: 2/5.
2025-06-18 22:38:48,721 [INFO] [app.agents.base_agent:816] - [SlideDetailerAgent] 尝试 1/5 使用密钥 '...ChBw'
2025-06-18 22:38:48,724 [INFO] [google_genai.models:6050] - AFC is enabled with max remote calls: 10.
2025-06-18 22:38:49,449 [INFO] [httpx:1025] - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 400 Bad Request"
2025-06-18 22:38:49,450 [ERROR] [app.agents.base_agent:892] - [SlideDetailerAgent] Instructor调用失败，出现不可重试的错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT 
type\n', 'status': 'INVALID_ARGUMENT'}}
Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 168, in retry_sync
    response = func(*args, **kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client_genai.py", line 66, in sync_wrapper
    return client.models.generate_content(*args, **kwargs)  # type:ignore
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 6058, in generate_content
    response = self._generate_content(
        model=model, contents=contents, config=parsed_config
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\models.py", line 5007, in _generate_content
    response_dict = self._api_client.request(
        'post', path, request_dict, http_options
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 927, in request
    response = self._request(http_request, stream=False)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\_api_client.py", line 793, in _request
    errors.APIError.raise_for_response(response)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\google\genai\errors.py", line 104, in raise_for_response
    raise ClientError(status_code, response_json, response)
google.genai.errors.ClientError: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': '* GenerateContentRequest.generation_config.response_schema.properties["key_elements"].items.properties: should be non-empty for OBJECT type\n', 'status': 'INVALID_ARGUMENT'}}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 163, in retry_sync
    for attempt in max_retries:
                   ^^^^^^^^^^^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 445, in __iter__
    do = self.iter(retry_state=retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 378, in iter
    result = action(retry_state)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\tenacity\__init__.py", line 421, in exc_check
    raise retry_exc from fut.exception()
tenacity.RetryError: RetryError[<Future at 0x20a62871250 state=finished raised ClientError>]

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\agents\base_agent.py", line 817, in _call_llm_with_instructor
    response_model = await asyncio.to_thread(
                     ^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "D:\miniconda3\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\miniconda3\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\client.py", line 366, in create
    return self.create_fn(
           ~~~~~~~~~~~~~~^
        response_model=response_model,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
        **kwargs,
        ^^^^^^^^^
    )
    ^
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\patch.py", line 193, in new_create_sync
    response = retry_sync(
        func=func,  # type: ignore
    ...<7 lines>...
        mode=mode,
    )
  File "E:\cursor\ppt\TikTodo-aippt\backend\app\..\..\venv\Lib\site-packages\instructor\retry.py", line 194, in retry_sync
    raise InstructorRetryException(
    ...<9 lines>...
    ) from e